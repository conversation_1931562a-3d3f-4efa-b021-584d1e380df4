@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;

  /* Dark Theme - Primary Background Colors */
  --background: oklch(0.04 0 0); /* #0a0a0b */
  --foreground: oklch(0.97 0 0); /* #f8fafc */
  --card: oklch(0.1 0 0); /* #1a1a1b */
  --card-foreground: oklch(0.97 0 0); /* #f8fafc */
  --popover: oklch(0.1 0 0); /* #1a1a1b */
  --popover-foreground: oklch(0.97 0 0); /* #f8fafc */

  /* Primary Accent - Blue */
  --primary: oklch(0.6 0.2 264); /* #3b82f6 */
  --primary-foreground: oklch(0.97 0 0); /* #f8fafc */

  /* Secondary Colors */
  --secondary: oklch(0.16 0 0); /* #2a2a2b */
  --secondary-foreground: oklch(0.97 0 0); /* #f8fafc */
  --muted: oklch(0.16 0 0); /* #2a2a2b */
  --muted-foreground: oklch(0.55 0 0); /* #64748b */

  /* Accent Colors */
  --accent: oklch(0.6 0.2 264); /* #3b82f6 */
  --accent-foreground: oklch(0.97 0 0); /* #f8fafc */
  --destructive: oklch(0.6 0.2 27); /* #ef4444 */
  --destructive-foreground: oklch(0.97 0 0); /* #f8fafc */

  /* Border and Input */
  --border: oklch(1 0 0 / 10%); /* rgba(255, 255, 255, 0.1) */
  --input: oklch(0.16 0 0); /* #2a2a2b */
  --ring: oklch(0.6 0.2 264); /* #3b82f6 */

  /* Chart Colors */
  --chart-1: oklch(0.6 0.2 264); /* Blue */
  --chart-2: oklch(0.65 0.15 162); /* Green */
  --chart-3: oklch(0.7 0.18 70); /* Orange */
  --chart-4: oklch(0.62 0.26 304); /* Purple */
  --chart-5: oklch(0.64 0.24 16); /* Red */

  /* Sidebar Colors */
  --sidebar: oklch(0.1 0 0); /* #1a1a1b */
  --sidebar-foreground: oklch(0.97 0 0); /* #f8fafc */
  --sidebar-primary: oklch(0.6 0.2 264); /* #3b82f6 */
  --sidebar-primary-foreground: oklch(0.97 0 0); /* #f8fafc */
  --sidebar-accent: oklch(0.16 0 0); /* #2a2a2b */
  --sidebar-accent-foreground: oklch(0.97 0 0); /* #f8fafc */
  --sidebar-border: oklch(1 0 0 / 10%); /* rgba(255, 255, 255, 0.1) */
  --sidebar-ring: oklch(0.6 0.2 264); /* #3b82f6 */
}



@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }

  code, pre {
    font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
  }
}

@layer components {
  /* Glass Card Base */
  .glass-card {
    background: oklch(1 0 0 / 5%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid oklch(1 0 0 / 10%);
    border-radius: 1rem;
    box-shadow:
      0 8px 32px oklch(0 0 0 / 30%),
      inset 0 1px 0 oklch(1 0 0 / 10%);
  }

  /* Glass Button */
  .glass-button {
    background: oklch(0.6 0.2 264 / 10%);
    backdrop-filter: blur(10px);
    border: 1px solid oklch(0.6 0.2 264 / 20%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .glass-button:hover {
    background: oklch(0.6 0.2 264 / 20%);
    border-color: oklch(0.6 0.2 264 / 40%);
    transform: translateY(-2px);
  }

  /* Glass Input */
  .glass-input {
    background: oklch(1 0 0 / 5%);
    backdrop-filter: blur(10px);
    border: 1px solid oklch(1 0 0 / 10%);
    transition: all 0.3s ease;
  }

  .glass-input:focus {
    background: oklch(1 0 0 / 8%);
    border-color: oklch(0.6 0.2 264 / 50%);
    box-shadow: 0 0 0 3px oklch(0.6 0.2 264 / 20%);
  }
}
